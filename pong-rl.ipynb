#%% md
# Policy Gradient from Pixels: Training Pong with PyTorch

本 Notebook 将演示如何用 **Policy Gradient (REINFORCE)** 方法从零开始训练一个能打 Atari **Pong** 的智能体。

### 主要里程碑
1. PyTorch 基础与自动求导
2. 图像预处理 → 神经网络输入
3. 构建两层 MLP PolicyNet
4. 折扣回报 $G_t$ 与 REINFORCE 梯度
5. 训练过程可视化与模型保存

> 建议按顺序逐单元执行；所有代码已在 Python 3.10 + PyTorch 2.x 环境测试通过。
#%% md
## 1. 安装与导入依赖
#%% md
**详细解释**  
本节仅做环境准备：
- `gymnasium[atari]` / `ale-py` 提供 Atari 环境接口。
- `autorom` 一次性自动下载官方 ROM（需接受许可）。
- `torch` 提供张量、自动微分和 optimizers。
- `matplotlib` 用于后续绘图。

本节不涉及核心数学公式，但后续所有梯度计算依赖 PyTorch 自动求导机制。
#%%
pip install "gymnasium[atari]" ale-py "autorom[accept-rom-license]" torch matplotlib --quiet
#%%
# 如果在 Colab，请先取消下一行注释安装依赖：
# !pip install gymnasium[atari] ale-py autorom[accept-rom-license] torch matplotlib --quiet

import gymnasium as gym
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import matplotlib.pyplot as plt
from collections import deque

if torch.backends.mps.is_available():
    device = 'mps'
elif torch.cuda.is_available():
    device = 'cuda'
else:
    device = 'cpu'

print('使用设备:', device)
#%% md
## 2. PyTorch 基础演示
#%% md
**详细解释**  
PyTorch 的核心概念是 *Tensor* 与 *Autograd*：
- **Tensor**：类似 NumPy ndarray，但可在 GPU 上运算。
- **Autograd**：自动构建计算图并在调用 `backward()` 时计算梯度。

示例函数：
$$y = \sum_{i=1}^{n} x_i^2$$
其梯度为：
$$\frac{\partial y}{\partial x_i} = 2x_i$$
代码验证如下。
#%%
x = torch.tensor([2.0, 3.0], requires_grad=True)
y = x.pow(2).sum()   # y = 2^2 + 3^2 = 13
y.backward()
print('x:', x)
print('梯度 dy/dx:', x.grad)  # 期待 [4.0, 6.0]
#%% md
## 3. Pong 图像预处理函数
#%% md
**详细解释**
原始 Atari 帧尺寸为 $210\times160\times3$。Karpathy 2016 经典做法：
1. **裁剪**(crop)：去掉计分板和边框 → $160\times160\times3$。
2. **下采样**(sub-sample)：每 2 像素取 1 → $80\times80\times3$。
3. **单通道提取**：仅保留 R 通道，因图像几乎是灰阶。
4. **背景抹除**：把背景像素值 144、109 置 0，其余置 1，得到二值图。
5. **展平**：$80\times80 = 6400$ 作为 MLP 输入。

数值映射公式：
$$I'(u,v)=\begin{cases}
0 & \text{if } I(u,v) \in \{144, 109\}\\
1 & \text{otherwise}
\end{cases}$$
#%%
def preprocess(img: np.ndarray) -> torch.Tensor:
    """将 210x160x3 图像转换为 6400-D float 向量"""
    img = img[35:195]          # 裁剪
    img = img[::2, ::2, 0]     # 下采样 + 取 R 通道
    img[img == 144] = 0        # 背景1
    img[img == 109] = 0        # 背景2
    img[img != 0] = 1          # 其它 → 1
    return torch.from_numpy(img.astype(np.float32).ravel()).unsqueeze(0)
#%% md
## 4. 策略网络（MLP）
#%% md
**详细解释**  
网络结构：
$$h = \operatorname{ReLU}(W_1 x + b_1)$$
$$p = \sigma(W_2 h + b_2)$$
其中 \(x \in \mathbb{R}^{6400}\)，隐藏层 \(h \in \mathbb{R}^{200}\)，输出 \(p\) 为“向上”动作概率（Pong 动作映射：UP=2, DOWN=3）。

损失采用 **Binary Cross-Entropy**：
$$\text{BCE}(p, y) = -\big[ y\,\log p + (1-y)\,\log(1-p) \big]$$
#%%
class PolicyNet(nn.Module):
    def __init__(self):
        super().__init__()
        self.fc1 = nn.Linear(80*80, 200)
        self.fc2 = nn.Linear(200, 1)

    def forward(self, x):
        x = x.float()
        x = F.relu(self.fc1(x))
        return torch.sigmoid(self.fc2(x))

policy = PolicyNet().to(device)
print('参数总数:', sum(p.numel() for p in policy.parameters()))
#%% md
## 5. 超参数设置
#%% md
**详细解释**  
- **learning_rate**：优化器步长，过大导致震荡，过小收敛慢。
- **gamma (折扣因子)**：权衡短期 vs. 长期奖励。
  
  折扣回报定义：
  $$G_t = \sum_{k=0}^{\infty} \gamma^k r_{t+k}$$
- **batch_size**：累计多少局（episode）后再更新一次参数，起到 gradient accumulation 作用。
#%%
learning_rate = 1e-3
gamma = 0.99
batch_size = 10
optimizer = optim.RMSprop(policy.parameters(), lr=learning_rate)
#%% md
### 5.1 折扣回报函数
#%% md
**详细解释**  
REINFORCE 使用 *Monte-Carlo* 估计的折扣回报：
$$\hat{G}_t = \sum_{k=0}^{T-t-1} \gamma^k r_{t+k}$$
实现时从最后一步向前递推，可避免重复计算。随后进行 **零均值 / 单位方差** 归一化，减少梯度方差。
#%%
def discount_rewards(rewards: torch.Tensor, gamma: float) -> torch.Tensor:
    discounted = torch.zeros_like(rewards)
    running_add = 0
    for t in reversed(range(rewards.size(0))):
        if rewards[t] != 0:
            running_add = 0  # Pong 每得分一次即 episode 子边界
        running_add = running_add * gamma + rewards[t]
        discounted[t] = running_add
    # 归一化
    discounted -= discounted.mean()
    discounted /= (discounted.std() + 1e-8)
    return discounted
#%% md
## 6. 训练主循环
#%% md
**详细解释**  
REINFORCE 梯度公式：
$$\nabla_{\theta} J(\theta) = \mathbb{E}_{\pi_{\theta}} \big[ \hat{G}_t \; \nabla_{\theta} \log \pi_{\theta}(a_t\,|\,s_t) \big]$$
实现步骤：
1. **采样**：用当前策略玩一局 Pong，存储 $(x_t, a_t, r_t, p_t)$。
2. **计算折扣回报** $\hat{G}_t$。
3. **BCE Loss** 与 $\hat{G}_t$ 做 element-wise 乘积，相当于 `policy-gradient`。
4. **反向传播** 更新参数。

> 训练 200 局左右通常即可正分战胜内置 AI（取决于随机种子）。
#%%
env = gym.make('ALE/Pong-v5', render_mode=None)
episode_number = 0
reward_history = []
running_mean = deque(maxlen=100)

batch_rewards, batch_probs, batch_actions = [], [], []
obs, _ = env.reset(seed=42)
prev_processed = None

while episode_number < 200:
    cur_processed = preprocess(obs)
    x = cur_processed - prev_processed if prev_processed is not None else torch.zeros_like(cur_processed)
    prev_processed = cur_processed

    prob = policy(x.to(device))
    action = 2 if np.random.uniform() < prob.item() else 3  # 2=UP, 3=DOWN

    obs, reward, terminated, truncated, _ = env.step(action)
    done = terminated or truncated

    batch_rewards.append(reward)
    batch_probs.append(prob)
    batch_actions.append(1 if action == 2 else 0)

    if done:
        episode_number += 1
        reward_history.append(sum(batch_rewards))
        running_mean.append(reward_history[-1])

        R = torch.tensor(batch_rewards, dtype=torch.float32)
        discounted_R = discount_rewards(R, gamma).to(device)
        probs = torch.cat(batch_probs).squeeze()
        actions = torch.tensor(batch_actions, dtype=torch.float32, device=device)

        loss = F.binary_cross_entropy(probs, actions, reduction='none')
        loss = (loss * discounted_R).mean()

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        print(f'第 {episode_number} 局 | 本局奖励: {reward_history[-1]:+} | 最近100局均值: {np.mean(running_mean):.3f}')

        batch_rewards, batch_probs, batch_actions = [], [], []
        obs, _ = env.reset()
        prev_processed = None

env.close()
#%% md
## 7. 绘制奖励曲线
#%% md
**详细解释**  
通过折线图直观观察智能体学习进度。理想趋势：
- 早期波动很大，平均奖励接近 -21（全负）。
- 随着训练，曲线逐渐上升并在 0 上下波动。

可进一步计算滑动平均或指数平滑来减少噪声：
$$\text{EMA}_t = \alpha r_t + (1-\alpha) \text{EMA}_{t-1}$$
#%%
plt.figure()
plt.plot(reward_history)
plt.title('Episode Reward')
plt.xlabel('Episode')
plt.ylabel('Reward')
plt.show()
#%% md
## 8. 保存 / 加载模型
#%% md
**详细解释**  
- 使用 `torch.save(state_dict)` 保存权重张量，不含计算图，可跨设备加载。
- 重新加载：
  ```python
  policy = PolicyNet(); policy.load_state_dict(torch.load('pong_pg.pth'))
  ```
- 建议连同超参数与环境版本一起记录，确保可复现性。
#%%
torch.save(policy.state_dict(), 'pong_pg.pth')
print('模型已保存到 pong_pg.pth')
#%% md
## 9. 可选：观看训练后的智能体
#%% md
**详细解释**  
利用 `render_mode='human'` 打开窗口可实时观看智能体决策。
- 推断时固定 `policy.eval()` 并关闭 `grad` 以加速。
- 简单阈值策略：$p>0.5$ 选择 UP，否则 DOWN。

观看时注意性能：CPU 渲染 ~60 FPS，Colab 无法弹窗，只能保存为 GIF 或视频。
#%%
# 运行下列代码将在本地弹出 Atari 窗口（Colab 不支持）：
# env = gym.make('ALE/Pong-v5', render_mode='human')
# obs, _ = env.reset()
# prev_processed = None
# done = False
# policy.eval()
# while not done:
#     cur_processed = preprocess(obs)
#     x = cur_processed - prev_processed if prev_processed is not None else torch.zeros_like(cur_processed)
#     prev_processed = cur_processed
#     with torch.no_grad():
#         prob = policy(x.to(device))
#     action = 2 if prob.item() > 0.5 else 3
#     obs, reward, terminated, truncated, _ = env.step(action)
#     done = terminated or truncated
# env.close()